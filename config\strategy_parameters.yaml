ace_config:
  adaptation_aggressiveness: 0.8
  complexity_threshold_calm: 0.25
  complexity_threshold_normal: 0.35
  complexity_threshold_volatile: 0.5
  dynamic_risk_config:
    dynamic_risk_parameters:
      atr_multiplier_base: 1.15
      atr_multiplier_volatility_factor: 0.6
      atr_period: 14
      max_adjustment_factor: 2.0
      min_adjustment_threshold: 0.05
      recalibration_frequency_minutes: 5
      regime_calm_multiplier: 0.8
      regime_normal_multiplier: 1.0
      regime_volatile_multiplier: 1.3
      take_profit_base_ratio: 1.2
      take_profit_volatility_adjustment: 0.3
      volatility_lookback_periods: 20
      volatility_threshold_high: 0.25
      volatility_threshold_low: 0.08
  enable_dynamic_risk_control: true
  encoders_extra:
  - class: OTOCEncoder
    name: OTOC_Scrambling
    params:
      data_keys:
      - btc_otoc_1h
      target_qubits:
      - 3
  entanglement_calm: linear
  entanglement_normal: linear_random_cx
  entanglement_volatile: full
  max_qubits: 10
  measure_frequency_calm: 2
  measure_frequency_normal: 1
  measure_frequency_volatile: 2
  min_qubits: 2
  override_on_metacognition: true
  quantum_sensitivity_refinements:
    adaptive_threshold_mode: true
    base_sensitivity_multiplier: 1.0
    coherence_sensitivity_threshold: 0.03
    dynamic_calibration:
      auto_recalibrate: true
      max_sensitivity: 2.0
      min_sensitivity: 0.3
      performance_window_size: 50
      recalibration_frequency_minutes: 15
      sensitivity_adjustment_rate: 0.1
    entropy_sensitivity_threshold: 0.02
    otoc_sensitivity_threshold: 0.05
    pattern_detection_sensitivity: 0.75
    regime_based_adjustments:
      high_volatility:
        coherence_weight: 0.9
        entropy_weight: 1.2
        otoc_weight: 1.4
        threshold_multiplier: 0.85
      low_volatility:
        coherence_weight: 1.3
        entropy_weight: 1.5
        otoc_weight: 0.8
        threshold_multiplier: 1.15
      normal:
        coherence_weight: 1.0
        entropy_weight: 1.0
        otoc_weight: 1.0
        threshold_multiplier: 1.0
    volatility_adaptive_factor: 0.3
  qubits_calm: 2
  qubits_normal: 8
  qubits_volatile: 8
  risk_profile: moderate
  scr_depth_calm: 3
  scr_depth_normal: 6
  scr_depth_volatile: 9
hyperparams:
  note: Parâmetros de amplificação e confiança movidos para hyperparams.yaml
  source: ../qualia/config/hyperparams.yaml
metacognition_config:
  buy_score_threshold: 0.38
  cooldown_confidence_step: 0.03
  cooldown_threshold: 2
  enhanced_pattern_detection:
    coherence_stability_threshold: 0.95
    entropy_change_threshold: 0.01
    otoc_variability_threshold: 0.08
    similarity_threshold_adaptive: true
  max_pnl_history_size: 250
  quantum_score_sigma: 0.015
  quantum_score_window_size: 75
  sell_score_threshold: 0.32
  skip_circuit_mutation_threshold: 2
qast_config:
  crossover_rate: 0.7
  diversity_weight: 0.4
  early_stop_threshold: 0.001
  elite_size: 12
  enable_elitism: true
  fitness_weight_consistency: 0.3
  fitness_weight_drawdown: 0.2
  fitness_weight_profitability: 0.5
  generations: 50
  generations_live: 5
  min_generations_before_stop: 10
  mutation_rate: 0.15
  param_bounds:
    meta_decision_threshold:
    - 0.05
    - 0.25
    s1_tsvf_window:
    - 10
    - 50
    s2_rsi_period:
    - 7
    - 25
    s2_sma_long_period:
    - 50
    - 200
    s2_sma_short_period:
    - 5
    - 30
    s3_tsvf_window:
    - 3
    - 12
    tsvf_vector_size:
    - 50
    - 150
  population_size: 120
  population_size_live: 6
  selection_pressure: 2.5
  tournament_size: 5
  use_adaptive_mutation: true
qmc_config:
  convert_mismatched_timeframes: true
  encoders:
  - encoders:
    - class: PriceMomentumEncoder
      name: PM
      params:
        data_keys:
        - BTC/USDT_1m_price_change
        name: PM
        scaling_factor: 50
        target_qubits:
        - 0
    - class: VolatilityEncoder
      name: VOL
      params:
        data_keys:
        - BTC/USDT_1m_volatility
        name: VOL
        target_qubits:
        - 1
    - class: VolumeRatioEncoder
      name: Volume_Ratio
      params:
        data_keys:
        - BTC/USDT_1m_volume_ratio
        name: VR
        target_qubits:
        - 2
    - class: RSIPhaseEncoder
      name: RSI_Phase
      params:
        data_keys:
        - BTC/USDT_1m_rsi
        name: RSI_P
        target_qubits:
        - 3
    - class: VolumeRatioAmplitudeEncoder
      name: Volume_Ratio_Amp
      params:
        data_keys:
        - BTC/USDT_1m_volume_ratio
        name: VR_AMP
        target_qubits:
        - 4
    id: default_qualia_encoders
  measure_frequency: 2
  qpu_steps: 6
  shots: 2048
  temperature: 0.08
  thermal_noise: true
  trading_primary_timeframe: 1m
  trading_timeframes:
  - 1m
qpm_config:
  enable_pattern_pruning: true
  enable_warmstart: true
  enhanced_retrieval:
    context_aware_ranking: true
    multi_modal_search: true
    sensitivity_based_filtering: true
    temporal_weighting: true
  max_patterns: 500
  pattern_ttl_seconds: 3600
  pca_target_dim: 1024
  persistence_path: data/cache/qpm_memory.json
  pruning_threshold: 0.15
  similarity_threshold: 0.2
  warmstart_min_patterns: 10
# Fibonacci Wave Hype Strategy Configuration
fibonacci_wave_hype_config:
  name: FibonacciWaveHypeStrategy
  enabled: true
  params:
    fib_lookback: 50
    sentiment_cache_ttl: 300  # 5 minutes

    # Configuração específica por timeframe
    timeframe_specific:
      "1m":
        hype_threshold: 0.42
        wave_min_strength: 0.32
        quantum_boost_factor: 1.02
        holographic_weight: 0.4
        tsvf_validation_threshold: 0.65

      "5m":
        hype_threshold: 0.36
        wave_min_strength: 0.26
        quantum_boost_factor: 1.05
        holographic_weight: 0.5
        tsvf_validation_threshold: 0.55

      "15m":
        hype_threshold: 0.28
        wave_min_strength: 0.22
        quantum_boost_factor: 1.08
        holographic_weight: 0.6
        tsvf_validation_threshold: 0.45

      "1h":
        hype_threshold: 0.22
        wave_min_strength: 0.18
        quantum_boost_factor: 1.12
        holographic_weight: 0.7
        tsvf_validation_threshold: 0.35

    fibonacci_levels:
      primary: [0.236, 0.382, 0.618]
      secondary: [0.146, 0.5, 0.786]
      extensions: [1.272, 1.618, 2.618]

    wave_detection:
      min_wave_bars: 5
      max_wave_bars: 20
      volume_confirmation: true
      momentum_threshold: 0.02

    risk_management:
      max_position_size: 0.1
      stop_loss_fib_level: 0.786
      take_profit_fib_level: 1.618
      dynamic_sizing: true

  integration:
    holographic_engine: true
    tsvf_calculator: true
    quantum_metrics: true
    sentiment_analysis: true

  backtesting:
    lookback_days: 90
    benchmark_strategies:
      - QualiaTSVFStrategy
      - EnhancedQuantumMomentumStrategy
    performance_metrics:
      - sharpe_ratio
      - max_drawdown
      - win_rate
      - profit_factor

quantum_universe_config:
  alpha: 0.15
  base_lambda: 0.05
  enhanced_metrics:
    adaptive_otoc_calculation: true
    harmonic_phase_detection: true
    regime_based_operators: true
    sensitivity_filtering: true
  hawking_factor: 0.05
  measure_frequency: 2
  otoc_frequency: 3
  qpu_steps: 6
  scr_depth: 4
  shots: 2048
  thermal_coefficient: 0.08
risk_profile_settings:
  aggressive:
    cooling_period_minutes: 30
    enable_dynamic_position_sizing: true
    enable_trailing_stop: true
    max_daily_loss_pct: 5.0
    max_drawdown_pct: 10.0
    max_open_positions: 3
    max_position_percentage: 0.08
    min_lot_size: 0.0001
    position_sizing_mode: kelly_criterion
    quantum_sensitivity_boost: 1.5
    risk_per_trade_pct: 1.0
    stop_loss_adjustment: 0.8
    stop_loss_percentage: 0.035
    take_profit_percentage: 0.06
  conservative:
    cooling_period_minutes: 30
    enable_dynamic_position_sizing: false
    enable_trailing_stop: false
    max_daily_loss_pct: 5.0
    max_drawdown_pct: 10.0
    max_open_positions: 1
    max_position_percentage: 0.02
    min_lot_size: 0.0001
    position_sizing_mode: fixed_percentage
    quantum_sensitivity_boost: 1.0
    risk_per_trade_pct: 1.0
    stop_loss_adjustment: 0.8
    stop_loss_percentage: 0.015
    take_profit_percentage: 0.025
  moderate:
    cooling_period_minutes: 30
    enable_dynamic_position_sizing: true
    enable_trailing_stop: true
    max_daily_loss_pct: 5.0
    max_drawdown_pct: 10.0
    max_open_positions: 2
    max_position_percentage: 0.05
    min_lot_size: 0.0001
    position_sizing_mode: volatility_adjusted
    quantum_sensitivity_boost: 1.2
    risk_per_trade_pct: 1.0
    stop_loss_adjustment: 0.8
    stop_loss_percentage: 0.025
    take_profit_percentage: 0.04
strategy_config:
  alternate_exchange: kucoin
  auto_download_history: true
  max_cache_age_minutes: 60
  name: EnhancedQuantumMomentumStrategy  # 🚀 ALTERADO: Usando estratégia corrigida
  ohlcv_failure_threshold: 3
  params:
    # 🚀 PARÂMETROS OTIMIZADOS DA ENHANCED QUANTUM MOMENTUM COM DYNAMIC PARAMETERS
    rsi_period: 14
    rsi_overbought: 70.0          # Base value - will be adjusted dynamically
    rsi_oversold: 30.0            # Base value - will be adjusted dynamically
    ema_short: 8
    ema_long: 21
    trend_ema: 50
    qubits: 8
    min_volatility: 0.007         # Optimized for dynamic adjustment
    max_volatility: 0.035         # Optimized for dynamic adjustment
    signal_threshold: 0.45        # Base threshold - will be adjusted dynamically
    quantum_weight: 0.55          # Base weight - will be adjusted dynamically
    adaptive_risk: true
    divergence_filter: false      # Disabled to allow more signals for optimization
    data_lookback_period: 200
    take_profit_r_multiple: 2.375 # Otimizado de 2.0
    stop_loss_r_multiple: 1.2     # Otimizado de 1.0
    log_level: "INFO"
    # 🚀 NEW: Dynamic Parameter Optimization
    use_dynamic_parameters: true  # Enable dynamic parameter optimization
    enable_enhanced_confidence: true  # Enable enhanced confidence calculation
  preload_candles_15m: 1500
  preload_candles_1h: 1500
  preload_candles_5m: 1500
temporal_detector_config:
  enhanced_sensitivity:
    anomaly_detection_threshold: 1.8
    pattern_recognition_threshold: 0.45
    reversal_detection_sensitivity: 0.35
    trend_detection_sensitivity: 0.25
  qft_shots: 6144
  quantum_wavelet_shots: 3072
  use_quantum_transform: true
  wavelet_depth: 4
universe_config:
  max_circuit_depth: 80
  max_circuit_operations: 250
  max_exact_qft_qubits: 14
  min_counts_diversity_ratio: 0.1
